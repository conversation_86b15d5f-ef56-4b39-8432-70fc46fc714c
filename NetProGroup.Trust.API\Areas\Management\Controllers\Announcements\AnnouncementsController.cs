// <copyright file="AnnouncementsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Announcements;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Announcements
{
    /// <summary>
    /// Use this controller for announcements related events.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]

    public class AnnouncementsController : TrustAPIControllerBase
    {
        private readonly IAnnouncementsAppService _announcementsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AnnouncementsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="announcementsAppService">The service for announcements.</param>
        public AnnouncementsController(
            ILogger<AnnouncementsController> logger,
            IAnnouncementsAppService announcementsAppService)
            : base(logger)
        {
            _announcementsAppService = announcementsAppService;
        }

        /// <summary>
        /// Gets the list of announcements that match the criteria.
        /// </summary>
        /// <remarks>
        /// Use the GeneralSearchTerm to search in either subject or announcement body.
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/announcements.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_ListAnnouncements")]
        [ProducesResponseType(typeof(PaginatedResponse<ListAnnouncementDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListAnnouncements(
            [FromQuery] FilterAnnouncementsDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                    {
                        Check.NotNull(request, nameof(request));
                    },

                    executeAsync: async _ =>
                    {
                        return await _announcementsAppService.FilterAnnouncementsAsync(request);
                    });

            return result.AsResponse();
        }

        /// <summary>
        /// Create a new announcement for the application.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/management/announcements.
        /// </remarks>
        /// <param name="data">The necessary data used to create an announcement.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "Management_Announcement_Create")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAnnouncement(CreateUpdateAnnouncementDTO data)
        {
            Guid item = Guid.Empty;
            var result = await ProcessRequestAsync<Guid>(
                validate: () =>
                {
                    Check.NotNull(data, nameof(data));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _announcementsAppService.CreateUpdateAnnouncementAsync(data);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Updates a new announcement for the application.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/management/announcements/{announcementId}.
        /// </remarks>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="data">The necessary data used to update an announcement.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpPut("{announcementId}")]
        [SwaggerOperation(OperationId = "Management_Announcement_Update")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAnnouncement(
            Guid announcementId,
            CreateUpdateAnnouncementDTO data)
        {
            Guid item = Guid.Empty;
            var result = await ProcessRequestAsync<Guid>(
                validate: () =>
                {
                    Check.NotNull(data, nameof(data));
                    Check.NotDefaultOrNull<Guid>(announcementId, nameof(announcementId));

                    data.Id = announcementId;
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _announcementsAppService.CreateUpdateAnnouncementAsync(data);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Retrieve an announcement given its id.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/management/announcements/{announcementId}.
        /// </remarks>
        /// <param name="announcementId">The announcementId as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpGet("{announcementId}")]
        [SwaggerOperation(OperationId = "Management_Announcement_GetById")]
        [ProducesResponseType(typeof(AnnouncementDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAnnouncementById(Guid announcementId)
        {
            AnnouncementDTO item = null;
            var result = await ProcessRequestAsync<AnnouncementDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(announcementId, nameof(announcementId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _announcementsAppService.GetAnnouncementByIdAsync(announcementId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Upload a document for an announcement.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/announcements/{announcementId}
        ///     {
        ///         data
        ///     }.
        /// </remarks>
        /// <param name="announcementId">The id of the announcement as Guid.</param>
        /// <param name="data">The data necessary to create an announcement document.</param>
        /// <response code="200">OK.</response>
        /// <response code="401">Unauthorized.</response>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("{announcementId}/documents")]
        [SwaggerOperation(OperationId = "Management_CreateAnnouncementDocument", Summary = "Upload a document for an announcement")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAnnouncementDocument(
            Guid announcementId,
            [SwaggerParameter(Description = "The data necessary to create an announcement document.")]
            [FromForm]
            CreateAnnouncementDocumentDTO data)
        {
            var result = await ProcessRequestAsync<object>(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(announcementId, nameof(announcementId));
                    Check.NotNull(data, nameof(data));
                    Check.NotNull(data.File, nameof(data.File));
                },

                executeAsync: async () =>
                {
                    await _announcementsAppService.CreateAnnouncementDocumentAsync(announcementId, data);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Deletes an announcement document..
        /// </summary>
        /// <remarks>
        /// This will delete the announcement document given its id.
        ///
        /// Sample request:
        ///
        ///     DELETE /api/v1/management/announcements/documents/{announcementDocumentId}?uploadComplete=true.
        ///         {
        ///
        ///         }.
        ///
        /// </remarks>
        /// <param name="announcementDocumentId">The id of the announcement document to delete as Guid.</param>
        /// <param name="uploadComplete">The value indicating whether the upload of documents is finished or not.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpDelete("documents/{announcementDocumentId}")]
        [SwaggerOperation(OperationId = "Management_DeleteAnnouncementDocument")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> DeleteAnnouncementDocument(
            Guid announcementDocumentId,
            bool uploadComplete)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(announcementDocumentId, nameof(announcementDocumentId));
                },
                executeAsync: async () =>
                {
                    await _announcementsAppService.DeleteAnnouncementDocumentAsync(announcementDocumentId, uploadComplete);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Deletes an announcement.
        /// </summary>
        /// <remarks>
        /// This will delete the announcement and the related data.
        ///
        /// Sample request:
        ///
        ///     DELETE /api/v1/management/announcements/{announcementId}
        ///         {
        ///
        ///         }.
        ///
        /// </remarks>
        /// <param name="announcementId">The id of the announcement to delete as Guid.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpDelete("{announcementId}")]
        [SwaggerOperation(OperationId = "Management_DeleteAnnouncement")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> DeleteAnnouncement(
            Guid announcementId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(announcementId, nameof(announcementId));
                },
                executeAsync: async () =>
                {
                    await _announcementsAppService.DeleteAnnouncementAsync(announcementId);
                });

            return result.AsResponse();
        }
    }
}